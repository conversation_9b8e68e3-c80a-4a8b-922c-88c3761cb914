/*=========== TABLE OF CONTENTS ===========
1. Common CSS
2. Page CSS
==========================================*/

body {
  padding-top: 101px;
}

/* Remove body padding-top on mobile */
@media only screen and (max-width: 1175px) {
  body {
    padding-top: 0 !important;
  }
}

details summary::-webkit-details-marker {
  display: none; 
}

/*-------------------------------------
  1. Common CSS
--------------------------------------*/
.dhi-group {
  cursor: pointer;
  &:hover > :last-child {
    display: block;
  }
}

.services {
  gap: var(--space-sm);
  display: flex;
  align-items: center;
  cursor: pointer;
}

.dhi-group-2 {
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  background-color: var(--white_a700);
  max-width: 800px;
  max-height: 500px;
  display: flex; 
  justify-content: center;
  align-items: start;
  position: absolute;
  top: 100%;
  right: 0;
  left: 13%;
  padding: 26px;
  margin: auto;
  z-index: 99;
  box-shadow: 0 4px 10px -2px rgba(0, 0, 0, 0.1);
  opacity: 0; 
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.dhi-group:hover .dhi-group-2,
.dhi-group:focus-within .dhi-group-2,
.dhi-group-2:hover {
  opacity: 1;
  visibility: visible;
}


@media only screen and (max-width: 550px) {
  .dhi-group-2 {
    flex-direction: column;
    position: relative;
    width: 100%;
    padding: var(--space-5xl);
  }
}


.dhi-group-3 {
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  background-color: var(--white_a700);
  max-width: 560px;
  display: flex;
  justify-content: center;
  align-items: start;
  position: absolute;
  top: 100%;
  right: 0;
  left: 30%;
  padding: 10px;
  margin: 0 auto;
  z-index: 99;
  box-shadow: 0 4px 10px -2px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.dhi-group:hover .dhi-group-3,
.dhi-group:focus-within .dhi-group-3,
.dhi-group-3:hover {
  opacity: 1;
  visibility: visible;
}

@media only screen and (max-width: 550px) {
  .dhi-group-3 {
    flex-direction: column;
    position: relative;
    width: 100%;
    padding: var(--space-5xl);
  }
}

.dropdown-image {
  width: 100%;
  margin-left: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dropdown-image img {
  max-width: 100%;
  max-height: 194px;
}


.menu-container {
  flex-wrap: wrap; 
  gap: 20px; 
  padding: 16px;
}

.menu {
  flex: 1; 
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mega-menu__title {
  font-size: 18px;
  font-weight: 600;
  color: #222A31;
  margin-bottom: 12px;
}

.mega-menu__text {
  font-size: 14px;
  color: #666;
}

.menu a:hover .mega-menu__text {
  color: #ff6600; 
}

.menu a {
  text-decoration: none;
}

.menu-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
}


@media (max-width: 768px) {
  .dhi-group-2 {
    width: 100%; 
    left: 0;
    right: 0;
  }

  .menu-container {
    flex-direction: column;
  }

  .menu {
    width: 100%;
  }
}


.services-link {
  position: relative;
  padding-right: 20px; 
  display: inline-block;
  text-decoration: none;
  color: inherit;
}

.services-link::after {
  content: '';
  display: inline-block;
    width: 19px;
    height: 8px; 
  background-image: url('../images/img_arrow_down.svg');
  background-size: cover;
  position: absolute;
  right: -15px;
  top: 50%;
  color:#222A31;
  transform: translateY(-50%);
  transition: transform 0.3s ease;
}

.services-link.arrow-rotated::after {
  transform: rotate(180deg);
  top: 40%;
}




.arrowdown_one {
  margin-left: 1px;
  margin-bottom: 15px;
  transition: transform 0.3s ease;
}

.arrowdown_one-mob {
  margin-left: 1px;
  transition: transform 0.3s ease;
}

.dhi-group:hover .arrowdown_one {
  transform: rotate(180deg); 
}

.rotated {
  transform: rotate(180deg);
}




.weoffer {
  gap: var(--space-md);
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.weoffer-1 {
  color: var(--black_900_7f) !important;
}

.weserve {
  gap: var(--space-md);
  align-self: center;
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: start;
}

.ui.heading.size-headings {
  font-size: 22px;
  font-weight: 700;
  line-height: 20.09px;
  margin-bottom: 20px;
  margin-left: 0px;
}
@media only screen and (max-width: 550px) {
  .ui.heading.size-headings {
    font-size: 18px !important;
    margin-bottom: 10px !important;
  }
  .getaninstant-span{
    font-size:15px !important;
  }
  .from-input, .to-input {
    width: 100%;
    padding: 3px !important;
    border: none;
    font-size: 13px !important;
    color: #5d4633;
    background-color: transparent;
    outline: none;
}
.call_one{
  width:33px !important;
  height:33px !important;
}
.styled-select {
    width: 100%;
    padding: 10px !important;
    font-size: 13px !important;
    color: #5a3e2e;
    background-color: #fff;
    border: 1px solid #CDA565;
    border-radius: 10px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
    outline: none;
    padding-right: 40px;}
.dropdown-icon{
      position: absolute;
    right: -32px !important;
    margin-top: 17px !important;
    transform: translateY(-50%);
    width: 90px !important;
    height: 6px !important;
    background-image: url(../images/drop-down.svg);
    background-size: contain;
    background-repeat: no-repeat;
    pointer-events: none;
}
}

.ui.heading {
  color: var(--blue_gray_900);
  font-family: 'Nohemi';
}

.column_one {
  gap: 6px;
  display: flex;
  flex-direction: column;
  @media only screen and (max-width: 1175px) {
   margin-top: 0px;
  }
}

.column_two {
  gap: var(--space-4xl);
  display: flex;
  flex-direction: column;
}

.column_three {
  gap: var(--space-md);
  display: flex;
  align-self: stretch;
  flex-direction: column;
}

.menu-container {
  width: 100%;
  padding: var(--space-5xl);
  border-radius: var(--radius-sm);
}

.menu-group {
  gap: var(--space-9xl);
  display: flex;
}

.mega-menu__link {
  color: var(--black_600) !important;
  font-size: 16px;
  font-weight: 400;
  align-self: center;
  @media only screen and (max-width: 550px) {
    font-size: 13px;
  }
}

.menu {
  gap: var(--space-3xl);
  display: flex;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    gap: var(--space-3xl);
  }
}

.mega-menu__title {
  color: var(--black_900) !important;
  font-size: 18px;
  font-weight: 700;
  @media only screen and (max-width: 550px) {
    font-size: 15px;
  }
}

.menu-column {
  gap: var(--space-lg);
  display: flex;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    gap: var(--space-lg);
  }
}

.menu-link {
  align-self: center;
  @media only screen and (max-width: 550px) {
    font-size: 13px;
  }
}

.mega-menu__text {
  color: var(--black_600) !important;
  font-size: 16px;
  font-weight: 400;
}

.home {
  font-weight: 400 !important;
}

.arrowdown_five {
  height: 4px;
}

.open {
  color: var(--gray_800);
  font-size: 14px;
  font-weight: 300;
  gap: var(--space-sm);
  display: flex;
}

.columneight {
  gap: var(--space-xs);
  display: flex;
  width: 22%;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 1175px) {
    gap: 5px;
    width: 100%;
  }
}

.eight {
  color: var(--white_a700) !important;
  @media only screen and (max-width: 550px) {
    font-size: 38px;
  }
}

.yearsin {
  color: var(--white_a700) !important;
}

.description-8 {
  color: var(--black_900_7f) !important;
  text-align: justify;
  width: 46%;
  line-height: 130%;
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }
}

.quoteonlineor-span-1 {
  font-weight: 700;
}

.columnlayerone-1 {
  gap: var(--space-13xl);
  display: flex;
  width: 50%;
  flex-direction: column;
  @media only screen and (max-width: 1175px) {
    width: 100%;
    margin-bottom: 30px;
  }

  @media only screen and (max-width: 550px) {
    gap: 27px;
  }
}

.rowlayerone_one {
  background-color: var(--blue_gray_900);
  padding: 58px 44px;
  border-radius: var(--radius-md);
  transition: background-color 0.3s, transform 0.3s;
}

@media only screen and (max-width: 1175px) {
  .rowlayerone_one {
    padding: var(--space-5xl);
  }
}

.layerone_one {
  height: 126px;
  width: 100%;
  @media only screen and (max-width: 1175px) {
    height: auto;
  }
}

.columnopencar {
  gap: var(--space-6xl);
  display: flex;
  flex-direction: column;
  align-items: start;
}

.columndescripti {
  gap: var(--space-sm);
  display: flex;
  align-self: stretch;
  flex-direction: column;
  align-items: start;
}

.columndescripti .name {
  font-family: 'Nohemi';
  font-size: 18px;
  font-weight: 300;
  color: #00000080;
  line-height: 26px;
  @media only screen and (max-width: 1175px) {
    font-size: 16px;
  }
}

.columndescripti .pt {
  font-family: 'Nohemi';
  font-size: 18px;
  font-weight: 300;
  color: #00000080;
  line-height: 26px;
  margin-left: 15px;
  @media only screen and (max-width: 1175px) {
    font-size: 16px;
    line-height: 21px;
  }
}

.pt-dot {
  color: #CDA565;
}

.columndescripti .txt {
  font-family: 'Nohemi';
  font-size: 18px;
  font-weight: 300;
  color: #00000080;
  line-height: 26px;
  @media only screen and (max-width: 1175px) {
    font-size: 16px;
    line-height: 20px;
  }
}

.description-11 {
  color: var(--black_900_7f) !important;
  text-align: justify;
  width: 100%;
  line-height: 130%;
}

.listlayerone {
  margin-top:0px;
  gap: var(--space-7xl);
  display: flex;
  @media only screen and (max-width: 1175px) {
    flex-direction: column;
  }
  @media only screen and (max-width: 768px) {
    margin-top:0px;
  }
}

.columndoortodoo {
  cursor: pointer;
  gap: var(--space-4xl);
  display: flex;
  background-color: var(--gray_300);
  width: 328px;
  flex-direction: column;
  padding: 26px var(--space-7xl);
  border-radius: var(--radius-md);
  transition: background-color 0.3s ease, color 0.3s ease;
  
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    padding: var(--space-5xl);
    max-height: 208px !important;
    min-height: 280px !important;
  }
}

.columndoortodoo:hover {
  background-color: #222A31; 
}

.columndoortodoo:hover h3, 
.columndoortodoo:hover h4, 
.columndoortodoo:hover h5, 
.columndoortodoo:hover h6, 
.columndoortodoo:hover p {
  color: #EAD4B9; 
}


.doortodoor {
  margin-top: 8px;
  line-height: 20px;
}

.description-4 {
  line-height: 130%;
  @media only screen and (max-width: 1175px) {
    font-size: 17px !important;
    line-height: 28.8px !important;
  }
}

.columnview {
  gap: var(--space-4xl);
  display: flex;
  width: 32%;
  flex-direction: column;
  @media (max-width: 1175px) {
    width: 100%;
  }
}

.stackview {
  height: 250px;
  position: relative;
  align-content: center;
  @media only screen and (max-width: 1175px) {
    height: auto;
  }
}

.image-container {
  cursor: pointer;
  position: relative;
  overflow: hidden; 
  border-radius: var(--radius-md);
}

.image-container .image-1 {
  height: 225px;
  flex: 1;
  object-fit: cover;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  border-radius: var(--radius-md);
  transition: transform 0.3s ease; 
  @media (max-width: 1175px) {
    height: 325px;
  }
  @media (max-width: 768px) {
    height: 225px;
  }
}

.image-container .view {
  height: 225px;
  background: linear-gradient(180deg, #222a3100, #1a20257f);
  flex: 1;
  position: absolute;
  left: 0px;
  bottom: 0px;
  right: 0px;
  top: -3px;
  margin: auto;
  border-radius: var(--radius-md);
  transition: background 0.3s ease;
}

.image-container:hover .image-1 {
  transform: scale(1.1); /* სურათის გაზრდა */
}

.image-container:hover .view {
  background: linear-gradient(180deg, #222a314d, #1a2025b3);
}


.columnbuyingaca {
  gap: var(--space-xs);
  display: flex;
  flex-direction: column;
}

.buyingacar {
  font-weight: 500 !important;
  line-height: 24px;
}

.description-13 {
  color: var(--blue_gray_900_7f) !important;
  font-weight: 400 !important;
  line-height: 17px;
}

.rowreadmore {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-5xl);
}

.showmore {
  color: var(--deep_orange_300) !important;
  font-weight: 500 !important;
  display: inline-block; 
  text-align: left; 
  margin-left: 0; 
}


.showmore:hover {
  color: #222a31 !important; 
}

.july162024 {
  align-self: end;
}

.rowbestprice {
  background-color: #EBE2D8; 
  width: 100%;
  display: flex;
  padding: var(--space-8xl) var(--space-2xl);
  border-radius: var(--radius-md);
  @media only screen and (max-width: 550px) {
    padding-top: var(--space-5xl);
    padding-bottom: var(--space-5xl);
  }
}


.columnbestprice {
  gap: var(--space-3xl);
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: start;
}

.bestprice {
  margin-left: 82px;
  @media only screen and (max-width: 1175px) {
    margin-left: 0px;
  }
}

.rowdescription-1 {
  gap: var(--space-xl);
  align-self: stretch;
  display: flex;
  justify-content: center;
  align-items: center;
  @media only screen and (max-width: 550px) {
    margin-left: -50px;
  }
}

.image-4 {
  height: 70px;
  width: 70px;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.description-16 {
  width: 80%;
  line-height: 17px;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.columnhome_one {
  gap: var(--space-2xl);
  display: flex;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    align-items: center;
  }
}

.description-20 {
  color: var(--black_900) !important;
}

.copyright2024 {
  color: var(--black_900_7f) !important;
}

.socialicon_one {
  height: 24px;
  width: 24px;
}

/*-------------------------------------
  2. Page CSS
--------------------------------------*/
.home-p {
  background-color: var(--gray_100);
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 550px) {
    display: list-item;
  }
}

/*-------------------------------------
  2.1 Menu CSS
--------------------------------------*/

.header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 3;
  background-color: var(--gray_100);
}

/* Remove fixed positioning on mobile */
@media only screen and (max-width: 1175px) {
  .header-fixed {
    position: relative !important;
    top: auto !important;
    left: auto !important;
  }
}


.header-fixed .header-content {
  max-width: 1375px; 
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100px;
  box-sizing: border-box;
  @media only screen and (max-width: 1430px) {
    padding-left: 50px;
    padding-right: 30px;
  }
  @media only screen and (max-width: 1175px) {
    height: 80px !important;
  }
}


.cta {
  display: flex;
  gap: 10px;
}

.burger-menu {
  display: none;
}

.rowhome {
  gap: 80px;
  display: flex;
  margin-top:15px;
  @media only screen and (max-width: 1365px) {
    gap: 30px;
    }
}

.rowhome .margin-left {
  margin-left: 200px;
  @media only screen and (max-width: 1070px) {
    margin-left: 100px;
    }
}


@media only screen and (max-width: 1175px) {
  .rowhome {
    display: none;
  }

  .header-content {
    display: flex !important;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding-left: 50px;
    padding-right: 30px;
  }

  .burger-menu {
    display: block;
    background: none;
    border: none;
    cursor: pointer;
    position: absolute;
    left: 17px;
    width: 22px;
    height: 22px;
    z-index: 10;
  }

  .headerlogo_one {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    margin-left: 0 !important;
    z-index: 5;
  }

  .cta {
    margin-left: auto;
    z-index: 10;
  }

  /* Hide the quote button on mobile, keep only the call button */
  .cta .quote {
    display: none;
  }

  .mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    left: -100%; 
    /*width: 100%;*/
    height: 100%;
    background-color: #EBE2D8;
    z-index: 999;
    flex-direction: column;
    justify-content: flex-start;
    text-align: left;
    padding: 40px 20px;
    overflow-y: auto;
    transition: left 1.0s ease; 
  }

  .mobile-menu.open {
    left: 0; 
    text-align: left;
    width: 65% !important;
  }

  body.menu-open {
     overflow: hidden; 
  }



  .mobile-menu ul {
    list-style: none;
    bottom: 0; 
    padding: 0;
    margin: 0;
  }

  .mobile-menu ul li {
    margin-left: 20px;
    margin: 15px 0;
    font-size: 24px;
    font-weight: 600;
    color: #222A31;
  }

  .mobile-menu ul li a {
    margin-left: 20px;
    text-decoration: none;
    color: inherit;
        font-size: 24px;
        line-height: 33.16px;
    font-weight: normal; 
  }

  .mobile-menu hr {
    border: none;
    border-width: 1px 0px 1px 0px;
    border-style: solid;
    border-color: #000000;
  }

  .dropdown-content {
    display: none;
    margin-top: 10px;
  }

  .dropdown-section {
    /*display: flex;*/
    /*justify-content: space-between;*/
    gap: 20px;
  }

  .dropdow-menu-p {
    color: #00000080; 
    font-size:9.19px !important; 
    font-weight:300; 
    line-height:9.23px !important; 
    margin-top:3px;
  }

  .dropdown-menu-ac {
    margin-left: 0px !important; 
    font-size: 9.19px !important; 
    line-height: 9.24px !important;
  }

  .dropdown-menu-aw {
    margin-left: 0px !important; 
    font-size: 12.26px !important; 
    line-height: 9.24px !important;
    font-weight: 600;
  }

  .dropdow-menu-pw {
    color: #00000080; 
    font-size:12px; 
    font-weight:300; 
    line-height:12.05px; 
    margin-top:3px;
  }

  .dropdown-left {
    flex: 1;
    width: 100%;
  }

  .dropdown-right {
    width: 123px;
  }

  .dropdown-left h4,
  .dropdown-right h4 {
    color: #00000080;
    font-size: 15.32px; 
    font-weight: 700;
    line-height: 15.39px;
    margin-bottom: 10px;
  }

  .dropdown-left ul,
  .dropdown-right ul {
    padding: 0;
    list-style-type: none;
  }

  .dropdown-left ul li,
  .dropdown-right ul li {
    margin-bottom: 15px;
    font-size: 14px; 
  }

  .dropdown-left ul li strong,
  .dropdown-right ul li strong {
    font-size: 12.26px;
  }

  .dropdown-right ul li {
    margin-bottom: 10px;
  }

  .dropdown-right ul li:last-child {
    margin-bottom: 0;
  }

  .close-menu {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 30px;
    background: none;
    border: none;
    cursor: pointer;
    width: 5%;
  }

  .dropdown-right img {
    width: 100%;
    height: auto;
  }

  .dropdown-item h4 {
    font-size: 16px; 
    font-weight: 600;
    margin: 0;
  }

  .dropdown-item p {
    font-size: 14px;
    color: #666;
    margin: 5px 0;
  }
}

@media only screen and (min-width: 551px) {
  .mobile-menu {
    display: none; 
  }
}



/*-------------------------------------
  2.1 #Menu CSS
--------------------------------------*/

.headerlogo_one {
  height: 39px;
  width: 230px;
  object-fit: contain;
  margin-left: -30px;

  @media only screen and (max-width: 1175px) {
    margin-left: 0;
    position: static;
    transform: none;
  }

  @media only screen and (max-width: 550px) {
    width: 150px;
    margin-top: 10px;
  }
}

.cta {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-right: -30px;
}

.quote {
  cursor: pointer;
  color: var(--white_a700);
  padding-left: var(--space-9xl);
  padding-right: var(--space-9xl);
  font-size: 16px;
  font-weight: 700;
  background-color: var(--deep_orange_300);
  text-align: center;
  height: 37px;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  
  @media only screen and (max-width: 550px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
    margin-left: auto;
  }
}

.quote:hover {
  background-color: var(--blue_gray_900);
  color: var(--deep_orange_300); 
  transform: scale(1.05); 
}

.call_one {
  cursor: pointer;
  height: 37px;
  padding-left: var(--space-lg);
  padding-right: var(--space-lg);
  background-color: var(--blue_gray_900);
  width: 37px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  
  @media only screen and (max-width: 1440px) {
    margin-left: 10px; 
    margin-right: 0;
  }
}

.call_one:hover {
  background-color: var(--deep_orange_300);
  transform: scale(1.05); 
}

@media only screen and (max-width: 1440px) {
  .cta {
    margin-left: 30px;
    margin-right: -18px;
    gap: 0px;
  }
}


/* აქედან */

.row_three {
  height: 100%;
  background-image: url(../images/Home-Page-Background.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
}

@media only screen and (max-width: 1175px) {
  .row_three {
    max-height: 100%;
    margin-top: -100px;
  }
}

.row_state {
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
}

@media only screen and (max-width: 1175px) {
  .row_state {
    max-height: 100%;
    margin-top: -100px;
  }
}

/* დორ-თუ-დორ */
.row_three-door {
  height: 100%;
  background-image: url(../images/services/9-Car-Hauler-Loaded.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* ოუფენ აუტო */
.row_three-open {
  height: 100%;
  background-image: url(../images/services/Home-Page-Background-4.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* ენქლოუს აუტო */
.row_three-enclosed {
  height: 100%;
  background-image: url(../images/services/Enclosed-Auto-Transport-Background.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* ექსპე აუტო */
.row_three-expedited {
  height: 100%;
  background-image: url(../images/services/Expeditet-Car-Shipping.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* კლასიკ აუტო */
.row_three-classic {
  height: 100%;
  background-image: url(../images/services/Classic-Car-Transport.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* ექსოტიკ აუტო */
.row_three-exotic {
  height: 100%;
  background-image: url(../images/services/Exotic-Car-Transport.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* მოტო აუტო */
.row_three-motorcycle {
  height: 100%;
  background-image: url(../images/services/Motorcycle-Shipping.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* არვ აუტო */
.row_three-rv {
  height: 100%;
  background-image: url(../images/services/RVs-on-Trailer.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* ბოუთ აუტო */
.row_three-boat {
  height: 100%;
  background-image: url(../images/services/Boat-Shipping.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* ჰივი აუტო */
.row_three-heavy {
  height: 100%;
  background-image: url(../images/services/Truck-shipping-background.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* ქოსთ აუტო */
.row_three-cost {
  height: 100%;
  background-image: url(../images/services/Classic-Semi-Truck-on-highway.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

/* ქროსს აუტო */
.row_three-across {
  height: 100%;
  background-image: url(../images/services/big-rig-car-carrier-desert.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
      margin-top: -100px;
  }
}

.getaninstant {
  font-size: 22px;
  font-weight: 700;
  color: #5d4633;
}

.getaninstant-span {
  font-size: 14px;
  margin-top: -5px;
  color: #7b6f65;
  display: block;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
  margin-left: 20px;
  margin-right: 20px;
}

.input-wrapper {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #CDA565;
  border-radius: 10px;
  box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.1);
}

.styled-select.input-error {
  border: 1px solid #fb0000 !important;
  border-radius: 10px;
}

.input-wrapper.input-error {
  border: 1px solid #fb0000 !important;
}


.form-control {
  width: 100%;
  border: none;
  font-size: 16px;
  padding: 10px;
  background-color: transparent;
  outline: none;
  color: #5d4633;
}

.styled-select {
  width: 100%;
  padding: 15px;
  font-size: 16px;
  color: #5a3e2e;
  background-color: #fff;
  border: 1px solid #CDA565;
  border-radius: 10px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  outline: none;
  padding-right: 40px; 
}

.dropdown-icon {
  position: absolute;
  right: -25px;
  margin-top: 25px;
  transform: translateY(-50%);
  width: 25px;
  height: 10px;
  background-image: url(../images/drop-down.svg);
  background-size: contain;
  background-repeat: no-repeat;
  pointer-events: none;
}

@media (max-width: 1175px) {
  .dropdown-icon {
    width: 100px;
  }
}

.styled-select:hover,
.styled-select:focus {
  border-color: #d0b69b;
}





.operable-section {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
  margin-left: 20px;
}

.vehicle_details:hover {
  background-color: #9d7642;
}


.input-wrapper img {
  margin-right: 10px;
}


.input-wrapper .icon {
  height: 18px;
  width: 17px;
}


.from-input,
.to-input {
  width: 100%;
  padding: 10px;
  border: none;
  font-size: 16px;
  color: #5d4633;
  background-color: transparent;
  outline: none;
}

.from-input::placeholder,
.to-input::placeholder {
  color: #a59b94;
  font-style: italic;
}

.ui.checkbox {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  font-family: Arial, sans-serif;
  font-size: 1em;
  color: #46351A;
}

.ui.checkbox input[type="radio"] {
  display: none; 
}

.ui.checkbox div {
  width: 17px;
  height: 17px;
  border: 2px solid #d0b69b;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  box-sizing: border-box;
  transition: background-color 0.2s ease;
}

.rowtransporttyp.radio-error .ui.checkbox div {
  border: 2px solid #fb0000;
  border-radius: 5px;
  padding: 5px;
}


.ui.checkbox input[type="radio"]:checked + div {
  background-color: #5a3e2e;
}

.vehicle_details {
  background-color: #b58544;
  color: #fff;
  padding: 15px;
  font-size: 16px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  text-transform: uppercase;
  width: 100%;
  max-width: none;
  text-align: center;
  margin-top: 20px;
  box-sizing: border-box; 
}

.vehicle_details:hover {
  background-color: #9d7642;
}

.operable-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.operable-section p {
  margin-right: 10px;
}


@media only screen and (max-width: 550px) {
  .input-group {
    flex-direction: column;
  }

  .quote-1 {
    padding: 0px;
  }

  .getaninstant {
    font-size: 18px;
  }

  .getaninstant-span {
    font-size: 12px;
  }

  .vehicle_details {
    width: 100%;
  }
}


.input-wrapper img {
  margin-right: 10px;
}


.input-wrapper .icon {
  height: 20px;
  width: 20px;
}




/* აქამდე */

.row_one {
  padding-top: 100px;
  padding-bottom: 250px;
  background-color: var(--black_900_8c);
  display: flex;
  justify-content: center;
  @media only screen and (max-width: 1175px) {
    padding-top: var(--space-5xl);
    padding-bottom: var(--space-5xl);
    max-height: 100%;
  }
}

.row {
  display: flex;
  justify-content: center;
  padding-right:100px;
  @media only screen and (max-width: 1175px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.rowquicksecure {
  gap: var(--space-xs);
  display: flex;
  align-items: center;
  @media only screen and (max-width: 1175px) {
    flex-direction: column;
  }
}

.columnquicksecu {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 1175px) {
    margin-top: 80px;
    align-self: stretch;
  }
}

.quicksecure {
  font-family: 'Nohemi';
  color: var(--white_a700) !important;
  font-size: 48px !important;
  width: 100%;
  line-height: 51px;
  @media only screen and (max-width: 550px) {
    font-size: 40px !important;
  }
}

.experience {
  color: var(--white_a700) !important;
  margin-top: 8px;
  margin-left: 4px;
  @media only screen and (max-width: 1175px) {
    margin-left: 0px;
    margin-top: 10px !important;
  }
}

.get_an_instant-2 {
  cursor: pointer;
  color: var(--white_a700);
  margin-left: 4px;
  padding-left: var(--space-10xl);
  padding-right: var(--space-10xl);
  font-size: 16px;
  font-weight: 700;
  margin-top: 10px;
  background-color: var(--deep_orange_300);
  text-align: center;
  height: 46px;
  min-width: 230px;
  max-width: 250px;
  border-radius: var(--radius-lg);
  transition: background-color 0.3s ease, color 0.3s ease;

  @media only screen and (max-width: 1175px) {
    display: none;
  }

  @media only screen and (max-width: 550px) {
    display: none;
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.get_an_instant-2:hover {
  background-color: #222A31; 
  transform: scale(1.05); 
}

.stackarrowdown {
  height: 346px;
  width: 25%;
  position: relative;
  @media only screen and (max-width: 1175px) {
    width: 100%;
    height: auto;
  }
}

.columnarrowdown {
  margin-top: 128px;
  gap: var(--space-11xl);
  display: flex;
  flex-direction: column;
  align-items: end;
}

.quote-1 {
  min-width: 350px;
  background-color: var(--gray_300);
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  bottom: 0px;
  right: 0px;
  top: 0px;
  height: max-content;
  margin: auto;
  border-radius: var(--radius-sm);
  box-shadow: 0px 5px 30px 5px rgb(0 0 0 / 50%);
}

.getaninstant {
  color: var(--gray_800) !important;
  margin-top: 28px;
  line-height: 30px;
}

.getaninstant-span {
  display: inline-flex;
  font-size: 16px;
  font-weight: 300;
}

.getaninstant-span-call {
  color: #222A31;
  text-decoration: none;
  transition: color 0.3s ease;
  margin-left: 10px;
  @media only screen and (max-width: 550px) {
    text-decoration: underline;
  }
}

.getaninstant-span-call:hover {
  color: #222A31; 
  text-decoration: underline;
  @media only screen and (max-width: 550px) {
    text-decoration: none;
  }
}

.getaninstant-span-call2 {
  display: inline-block;
  color: #CDA565;
  text-decoration: none;
  transition: color 0.3s ease;
}

.getaninstant-span-call2:hover {
  color: #222A31; 
  text-decoration: underline;
}

.getaninstant-span-link {
  display: inline-block;
  color: #fff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.getaninstant-span-link:hover {
  color: #CDA565; 
  text-decoration: underline;
}


.fromzip_or {
  color: var(--gray_800);
  margin-top: 26px;
  margin-left: 32px;
  margin-right: 36px;
  padding-left: var(--space-xl);
  padding-right: 33px;
  font-size: 14px;
  font-weight: 300;
  gap: var(--space-7xl);
  background-color: var(--gray_100);
  align-self: stretch;
  text-align: center;
  height: 44px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--deep_orange_100);
  @media only screen and (max-width: 1175px) {
    margin-left: 0px;
    margin-right: 0px;
  }

  @media only screen and (max-width: 550px) {
    padding-right: var(--space-5xl);
  }
}

.left-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 12px;
  height: 16px;
}

.linkedin {
  height: 16px;
  width: 12px;
}

.tozip_or {
  color: var(--gray_800);
  margin-top: 14px;
  margin-left: 32px;
  margin-right: 36px;
  padding-left: 11px;
  padding-right: 33px;
  font-size: 14px;
  font-weight: 300;
  gap: var(--space-5xl);
  background-color: var(--gray_100);
  align-self: stretch;
  text-align: center;
  height: 44px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--deep_orange_100);
  @media only screen and (max-width: 1175px) {
    margin-left: 0px;
    margin-right: 0px;
  }

  @media only screen and (max-width: 550px) {
    padding-right: var(--space-5xl);
  }
}

.left-icon-wrapper-1 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 16px;
}

.linkedin-1 {
  height: 16px;
  width: 18px;
}

.rowtransporttyp {
  display: flex;
  justify-content: flex-start;
  gap: 20px;
  margin-bottom: 25px;
  margin-top: 18px;
  margin-left: 25px;
  margin-right: 32px;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
    margin-left: 20px;
    margin-right: 0px;
  }
}

.columntransport {
  gap: var(--space-md);
  display: flex;
  flex-direction: column;
  align-items: start;
}

.transporttype {
  color: var(--gray_800) !important;
  font-weight: 500 !important;
}

.rowopen {
  gap: var(--space-5xl);
  align-self: stretch;
  display: flex;
  align-items: center;
}



.column_onee {
  margin-top: 136px;
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
}


.column {
  display: flex;
  flex-direction: column;
  @media only screen and (max-width: 1175px) {
    gap: var(--space-4xl);
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }

  @media only screen and (max-width: 550px) {
    gap: var(--space-4xl);
  }
}

.columnpremieral {
  margin-top: 50px;
  gap: var(--space-4xl);
  display: flex;
  flex-direction: column;
  align-items: start;
}

.premieralabama {
  margin-left: 6px;
  @media only screen and (max-width: 1175px) {
    margin-left: 0px;
  }

  @media only screen and (max-width: 550px) {
    font-size: 32px;
  }
}

.description-1 {
  color: var(--black_900_7f) !important;
  margin-left: 6px;
  width: 100%;
  line-height: 130%;
  @media only screen and (max-width: 1175) {
    margin-left: 0px;
  }
}

.roweight {
  background-color: var(--blue_gray_900);
  align-self: stretch;
  padding: 44px;
  border-radius: var(--radius-md);
  max-height: 190px;
  margin-bottom: -50px;
  @media only screen and (max-width: 1175px) {
    padding: var(--space-5xl);
    min-height: 280px;
    width: 100%;
    max-width: 300px;
    max-height: 310px;
    margin: 0px auto 20px auto;
    display: none;
  }
}

.listeight {
  gap: var(--space-12xl);
  display: flex;
  @media only screen and (max-width: 1175px) {
    flex-direction: column;
    gap: 5px;
  }
}

.columnshippinga {
  margin-top: 5px;
  gap: var(--space-8xl);
  display: flex;
  flex-direction: column;
}

.listshippingaca {
  margin-right: 68px;
  gap: var(--space-14xl);
  display: flex;
  @media only screen and (max-width: 1175px) {
    flex-direction: column;
    margin-right: 0px;
  }
}

.shippingacar {
  font-size: 24px;
  width: 52%;
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    font-size: 32px;
  }
}

.shippingacar-1 {
  font-size: 24px;
  width: 46%;
  @media only screen and (max-width: 1175px) {
    width: 100%;
    display: none;
  }

  @media only screen and (max-width: 550px) {
    font-size: 32px;
  }
}

.rowdescription {
  display: flex;
  justify-content: space-between;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1175px) {
    flex-direction: column;
  }
}

.stackseehowit {
  height: auto;
  margin-top: 84px;
  width: 84%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.column_three {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 20px;
  width: 100%;
}

.columnbooktwo, .column_two, .columnsafeandti {
  flex: 1;
  padding: 20px;
}

.columnstepcount {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.booktwo_one, .carkeyone_one, .truckone_one {
  width: 100px;
  height: auto;
  margin-right: 20px;
}

.rowbooktwo_one, .rowcarkeyone {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

h2, h3, h4 {
  margin-bottom: 10px;
  color: #222A31;
}

p {
  margin-bottom: 15px;
  color: #222A31;
}

@media screen and (max-width: 768px) {
  .column_three {
    flex-direction: column;
  }

  .rowbooktwo_one, .rowcarkeyone {
    flex-direction: column;
  }

  .booktwo_one, .carkeyone_one {
    margin-right: 0;
    margin-bottom: 10px;
  }
}


.columnbooktwo {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.columnseehowit {
  padding-left: 56px;
  padding-right: 56px;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 1175px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.rowbooktwo_one {
  margin-top: 50px;
  position: relative;
  align-self: stretch;
  display: flex;
  align-items: center;
  @media only screen and (max-width: 1175px) {
    flex-direction: column;
  }
}

.booktwo_one {
  height: 460px;
  width: 460px;
  object-fit: cover;
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }
}

.columnstepcount {
  background-color: var(--gray_300);
  display: flex;
  flex-direction: column;
  align-items: end;
  padding: var(--space-7xl) 70px var(--space-7xl) 56px;
  border-radius: var(--radius-md);
  @media only screen and (max-width: 1175px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }

  @media only screen and (max-width: 550px) {
    padding: var(--space-5xl);
  }
}

.stepcounter-2 {
  margin-left: 328px;
  text-transform: capitalize;
  align-self: start;
  @media only screen and (max-width: 1175px) {
    margin-left: 0px;
  }

  @media only screen and (max-width: 550px) {
    font-size: 28px;
  }
}

.quoteonlineor {
  color: var(--black_900) !important;
  margin-top: 8px;
}

.quoteonlineor-span {
  font-weight: 500;
}

.description-10 {
  color: var(--black_900_7f) !important;
  margin-bottom: 10px;
  margin-right: 206px;
  width: 58%;
  line-height: 16px;
  @media only screen and (max-width: 1175px) {
    width: 100%;
    margin-right: 0px;
  }
}

.column_twoo {
  margin-top: -8px;
  position: relative;
  @media only screen and (max-width: 1175px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.columnguardiana {
  background-color: var(--gray_300);
  display: flex;
  flex-direction: column;
  align-items: start;
  padding: var(--space-7xl) 52px;
  border-radius: var(--radius-md);
  @media only screen and (max-width: 1175px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }

  @media only screen and (max-width: 550px) {
    padding: var(--space-5xl);
  }
}

.stepcounter-1 {
  margin-left: 6px;
  text-transform: capitalize;
  @media only screen and (max-width: 1175px) {
    margin-left: 0px;
  }

  @media only screen and (max-width: 550px) {
    font-size: 28px;
  }
}

.guardianauto {
  color: var(--black_900) !important;
  margin-top: 8px;
  margin-left: 6px;
  font-weight: 500 !important;
  @media only screen and (max-width: 1175px) {
    margin-left: 0px;
  }
}

.description-3 {
  color: var(--black_900_7f) !important;
  margin-bottom: 74px;
  width: 46%;
  line-height: 16px;
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }
}

.rowcarkeyone {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 1.1px;
  margin-bottom: auto;
  right: 0px;
  left: 0px;
  margin-top: auto;
  @media only screen and (max-width: 1175px) {
    flex-direction: column;
    position: relative;
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.carkeyone_one {
  height: 430px;
  z-index: 1;
  width: 430px;
  position: relative;
  object-fit: cover;
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }
}

.columnsafeandti {
  margin-left: -426px;
  position: relative;
  background-color: var(--gray_300);
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: end;
  padding: var(--space-7xl) 166px var(--space-7xl) 56px;
  border-radius: var(--radius-md);
  @media only screen and (max-width: 1175px) {
    align-self: stretch;
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
    margin-left: 0px;
  }

  @media only screen and (max-width: 550px) {
    padding: var(--space-5xl);
  }
}

.stepcounter {
  margin-left: 242px;
  text-transform: capitalize;
  align-self: start;
  @media only screen and (max-width: 1175px) {
    margin-left: 0px;
  }

  @media only screen and (max-width: 550px) {
    font-size: 28px;
  }
}

.safeandtimely {
  color: var(--black_900) !important;
  margin-top: 8px;
  margin-right: 130px;
  font-weight: 500 !important;
  @media only screen and (max-width: 1175px) {
    margin-right: 0px;
  }
}

.description-2 {
  color: var(--black_900_7f) !important;
  margin-bottom: 46px;
  width: 62%;
  line-height: 16px;
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }
}

.truckone_one {
  height: 504px;
  width: 504px;
  object-fit: cover;
  position: absolute;
  right: -1px;
  bottom: 0px;
  top: 0px;
  margin-top: auto;
  margin-bottom: auto;
}

.columnlayerone {
  margin-top: 100px;
  gap: 100px;
  display: flex;
  flex-direction: column;
  @media only screen and (max-width: 1175px) {
    gap: 97px;
    margin-top: 50px
  }

  @media only screen and (max-width: 768px) {
    gap: 50px;
    margin-top: 30px
  }
}

.columnlayerone_land {
  margin-top: 100px;
  gap: 100px;
  display: flex;
  flex-direction: column;
  @media only screen and (max-width: 1175px) {
    gap: 97px;
    margin-top: 50px
  }

  @media only screen and (max-width: 768px) {
    gap: 50px;
    margin-top: 70px
  }
}

.columnlayerone_min {
  margin-top: 70px;
  gap: 100px;
  display: flex;
  flex-direction: column;
  @media only screen and (max-width: 1175px) {
    gap: 97px;
    margin-top: 50px
  }

  @media only screen and (max-width: 768px) {
    gap: 50px;
    margin-top: 30px
  }
}

.hide-mob {
  @media only screen and (max-width: 768px) {
    display: none;
  }
}

.hide-desk {
  @media only screen and (min-width: 768px) {
    display: none;
  }
}

.columnreadytobo {
  padding-left: var(--space-xs);
  padding-right: var(--space-xs);
  gap: var(--space-4xl);
  display: flex;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    margin-top: -20px
  }
}

@media only screen and (max-width: 768px) {
  .columnreadytobo .ui.heading.size-headingmd {
    font-size: 27px !important;
  }
}


.description {
  color: var(--black_900_7f) !important;
  width: 100%;
  line-height: 130%;
}

.get_an_instant {
  cursor: pointer;
  color: var(--white_a700);
  padding-left: var(--space-10xl);
  padding-right: var(--space-10xl);
  font-size: 16px;
  font-weight: 700;
  background-color: var(--deep_orange_300);
  text-align: center;
  height: 46px;
  min-width: 230px;
  max-width: 310px;
  border-radius: var(--radius-md);
  transition: background-color 0.1s ease, transform 0.1s ease; 
  
  @media only screen and (max-width: 550px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
    max-width: 100%;
  }
}

.get_an_instant:hover {
  background-color: #222A31; 
  transform: scale(1.05); 
}



@media only screen and (max-width: 768px) {
  .map {
    display: none;
  }
}

@media only screen and (min-width: 768px) {
  .mob-map {
    display: none;
  }
}

.map-mob-f-container {
  background-color: #222A31;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  width: 100%;
  max-width: 768px; 
  margin-top: 0px;
  position: relative;
}

.map-mob-f-heading {
  font-size: 16px;
  margin-bottom: 10px;
  color: #CDA565;
  font-weight: 700;
}

.map-mob-f-subheading {
  font-size: 10px;
  color: #EBE2D8;
  margin-bottom: 20px;
  font-weight: 300;
}

.map-mob-f-state-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-left: -10px;
  margin-right: -10px;
}

.map-mob-f-states-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.map-mob-f-state-button {
  background-color: #EBE2D8;
  color: #222A31;
  border: none;
  font-size: 0.8rem; 
  cursor: pointer;
  text-align: center;
  transition: background-color 0.3s ease, transform 0.2s ease;
  white-space: normal; 
  width: calc(50% - 10px); 
  min-height: 40px; 
  box-sizing: border-box;
  flex-grow: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}


.map-mob-f-state-button:hover {
  background-color: #ddd;
  transform: scale(1.05);
}


.map-mob-f-nav-button {
  background: none;
  border: none;
  color: #EAD4B9;
  width: 14px;
  height: 14px;
  cursor: pointer;
  transition: color 0.3s ease, transform 0.2s ease;
  position: relative;
}

.arrow-map-left {
  margin-left: -5px;
  width: 14px;
  height: 14px;
  max-width: 14px;
}

.arrow-map-right {
  margin-left: 5px;
  width: 14px;
  height: 14px;
  max-width: 14px;
}

.map-mob-f-pagination {
    position: absolute;
    top: 83%;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transform: translateY(-50%);
}

.map-mob-f-dot {
  height: 8px;
  width: 8px;
  background-color: #555;
  border-radius: 50%;
  display: inline-block;
  transition: background-color 0.3s ease;
}

.map-mob-f-dot.map-mob-f-active {
  background-color: #EAD4B9;
}

.columnwhichstat {
  margin: 0 auto;
  max-width: 900px;
  gap: var(--space-md);
  display: flex;
  flex-direction: column;
  align-items: center;
}



.whichstatedo {
  text-align: center;
  margin-top: 20px;
  line-height: 48px;
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    font-size: 38px;
  }
}

.image {
  height: 758px;
  width: 100%;
  @media only screen and (max-width: 1175px) {
    height: auto;
  }
}

.column_four {
  margin-top: 100px;
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 1175px) {
    margin-top: 100px;
  }
  @media only screen and (max-width: 550px) {
    margin-top: 0px;
  }
}

.columnexperienc {
  margin-top:100px;
  gap: var(--space-4xl);
  display: flex;
  flex-direction: column;
  @media only screen and (max-width: 1175px) {
    text-align: left;
    /* padding-left: var(--space-5xl); */
    /* padding-right: var(--space-5xl); */
  }

  @media only screen and (max-width: 768px) {
    margin-top: 30px;
    text-align: left;
  }
}

.experience_one {
  text-align: center;
  line-height: 48px;
  @media only screen and (max-width: 768px) {
    text-align: left;
    font-size: 18px !important;
    line-height: 22.1px;
  }
}

.column_five {
  margin-top: 100px;
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 768px) {
    margin-top: 50px;
  }
}

.columnmostrecen {
  gap: var(--space-4xl);;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.listview {
  gap: var(--space-7xl);
  display: flex;
  align-self: stretch;
  @media only screen and (max-width: 1175px) {
    flex-direction: column;
  }
}

.see_all {
  cursor: pointer;
  color: var(--gray_100);
  padding-left: var(--space-10xl);
  padding-right: var(--space-10xl);
  font-size: 16px;
  font-weight: 500;
  background-color: var(--blue_gray_900);
  text-align: center;
  height: 46px;
  min-width: 172px;
  max-width: 200px;
  border-radius: var(--radius-md);
  transition: background-color 0.3s ease, color 0.3s ease;
  
  @media only screen and (max-width: 550px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.see_all:hover {
  background-color: #CDA565; 
  color: var(--gray_50); 
}


.column_six {
  margin-top: 100px;
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media only screen and (max-width: 550px) {
  .column_six {
    margin-top: 30px;
    display: none; 
  }
}



.column_six-mob {
  margin-top: 50px;
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media only screen and (min-width: 550px) {
  .column_six-mob {
    display: none; 
  }
}

.columnwhychoose {
  gap: 42px;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 1175px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.whychoose {
  text-align: center;
  width: 64%;
  line-height: 48px;
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    text-align: left;
    font-size: 20px !important;
  }
}

.home_p {
  align-self: stretch;
  display: grid;
  gap: var(--space-7xl);
  grid-template-columns: repeat(2, minmax(0, 1fr));
  justify-content: center;
  @media only screen and (max-width: 1175px) {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

.column_eight {
  margin-top: 100px;
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 1175px) {
    margin-top: 30px;
  }
}

.column_seven {
  gap: 144px;
  display: flex;
  flex-direction: column;
  @media only screen and (max-width: 1175px) {
    gap: 108px;
    /* padding-left: var(--space-5xl); */
    /* padding-right: var(--space-5xl); */
  }

  @media only screen and (max-width: 550px) {
    gap: 72px;
  }
}

.columnfrequentl {
  margin-left: 94px;
  margin-right: 90px;
  gap: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 1175px) {
    text-align: left;
    margin-left: 0px;
    margin-right: 0px;
    gap: 10px;
    margin-top: 30px;
  }
}

.columnfrequentl-t {
  align-items: center;
  @media only screen and (max-width: 1175px) {
    text-align: left;
  }
}

.expandablelistc {
  padding-left: var(--space-xs);
  padding-right: var(--space-xs);
  gap: var(--space-10xl);
  display: flex;
  align-self: stretch;
  flex-direction: column;
}

.rowclose_one {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  cursor: pointer;
  padding-left: 10px;
}

.rowclose_one::before {
  content: "";
  position: absolute;
  left: 0;
  top: 1;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  background-repeat: no-repeat;
  background-size: contain;
  transition: background-image 0.3s ease;
}

details:not([open]) .rowclose_one::before {
  background-image: url('../images/img_plus.svg');
}

details[open] .rowclose_one::before {
  background-image: url('../images/img_close.svg');
}


.howlongdoes {
  margin-left: 16px;
  align-self: center;
  @media only screen and (max-width: 550px) {
    font-size: 16px !important;
    }
}

.vector-5 {
  background-color: var(--blue_gray_900_7f);
  width: 100%;
  height: 1px;
  margin-left: -10px;
}

.instantquote {
  margin: 100px auto 0px auto;
  max-width: 1152px;
  gap: var(--space-5xl);
  display: flex;
  background-color: var(--blue_gray_900);
  flex-direction: column;
  align-items: center;
  padding: var(--space-6xl);
  text-align: center;
  border-radius: 10px;
}

@media only screen and (max-width: 1175px) {
  .instantquote {
  margin-top: 50px;
  margin-bottom: -100px;
  border-radius: 0px;
  max-width: 1175px;
  }
}

  @media only screen and (max-width: 550px) {
    .instantquote {
    margin-top: 30px;
    margin-bottom: -50px;
    border-radius: 0px;
    }

.instantquote2 {
  margin-top: 100px;
  gap: var(--space-5xl);
  display: flex;
  background-color: var(--blue_gray_900);
  flex-direction: column;
  align-items: center;
  padding: var(--space-6xl);
  border-radius: var(--radius-md);
  text-align: center;
  @media only screen and (max-width: 550px) {
      margin-top: 30px;
      padding: var(--space-5xl);
      border-radius: 5px;
  }
}




  .columnwantan {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .wantan {
    font-size: 24px;
    font-weight: bold;
    color: var(--white_a700) !important;
    @media only screen and (max-width: 550px) {
      font-size: 20px !important;
    }
  }

  .useour {
    font-size: 14px !important;
    line-height: 1.5;
    color: var(--white_a700);
    margin-top: 50px;
  }

  .useour-span {
    display: inline;
    margin-top: -30px;
  }

  .useour-span-1 {
    display: block; 
    color: var(--deep_orange_300);
    text-decoration: none;
    font-size: 18px; 
    margin-top: 5px; 
  }

  .useour-span-1:hover {
    text-decoration: underline;
  }

  .get_an_instant-1 {
    font-size: 16px;
    font-weight: bold;
    color: var(--blue_gray_900);
    background-color: var(--deep_orange_300);
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    transition: background-color 0.3s ease;
    margin-top: 0px; 
  }

  .get_an_instant-1:hover {
    background-color: var(--deep_orange_400);
  }
}


.columnwantan {
  gap: 2px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.wantan {
  color: var(--white_a700) !important;
  @media only screen and (max-width: 550px) {
    font-size: 28px;
  }
}

.useour {
  color: var(--white_a700) !important;
  display: flex;
}

.useour-span {
  color: var(--white_a700);
}

.useour-span-1 {
  color: var(--deep_orange_300);
  display: inline;
  text-decoration: none; 
}

.useour-span-1:hover {
  text-decoration: underline; 
}


.get_an_instant-1 {
  cursor: pointer;
  color: var(--blue_gray_900);
  margin-bottom: 14px;
  padding-left: var(--space-10xl);
  padding-right: var(--space-10xl);
  font-size: 16px;
  font-weight: 700;
  background-color: var(--deep_orange_300);
  text-align: center;
  height: 46px;
  min-width: 236px;
  max-width: 250px;
  border-radius: var(--radius-md);
  @media only screen and (max-width: 550px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.get_an_instant-1:hover {
  color: white; 
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); 
}

.footer {
  margin-top: 100px;
  display: flex;
  background-color: var(--gray_300);
  align-self: stretch;
  justify-content: center;
  align-items: end;
  padding: var(--space-4xl);
  @media only screen and (max-width: 550px) {
    margin-top: 30px;
  }
}

.columnfooterlog {
  margin-top: 42px;
  gap: 48px;
  display: flex;
  width: 84%;
  flex-direction: column;
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }
}

.rowfooterlogo {
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1175px) {
    flex-direction: column;
    align-items: center;
  }
}

.footerlogo_one {
  height: 120px;
  width: 328px;
  object-fit: contain;
}

.rowcontact_two {
  align-self: center;
  width: 66%;
  display: flex;
  justify-content: center;
  align-items: center;
  @media only screen and (max-width: 1175px) {
    flex-direction: column;
    width: 100%;
  }
}

.rownavigation {
  width: 60%;
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1175px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    flex-direction: column;
  }
}

.columnnavigatio {
  gap: var(--space-lg);
  display: flex;
  width: 40%;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    width: 100%;
    margin-top:50px;
    align-items: center;
  }
}

.columnservices {
  gap: var(--space-lg);
  align-self: center;
  display: flex;
  width: 60%;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    margin-top:50px;
    width: 100%;
    align-items: center;
  }
}

.columnemail {
  width: 38%;
  display: flex;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 1175px) {
    width: 100%;
    align-items: center;
  }
}

.columncontact {
  gap: var(--space-4xl);
  display: flex;
  align-self: stretch;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 1175px) {
    margin-top:50px;
    text-align: center;
    align-items: center;
  }
}

.description-link {
  line-height: 16px;
}

.email {
  color: var(--black_900) !important;
  margin-top: 20px;
}

.class-18778788008 {
  color: var(--black_900) !important;
  margin-top: 14px;
}

.rowsocialicon {
  margin-top: 28px;
  width: 54%;
  display: flex;
  justify-content: space-between;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1175px) {
    width: 50%;
  }
}

.rowcopyright202 {
  margin-left: 38px;
  margin-right: 38px;
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1175px) {
    margin-left: 0px;
    margin-right: 0px;
  }

  @media only screen and (max-width: 550px) {
    flex-direction: column;
    align-items: center;
  }
}

.rowtermandcondi {
  gap: var(--space-4xl);
  align-self: center;
  display: flex;
  align-items: center;
}

.termandconditio-link {
  color: -webkit-link;
}

.lineone_one {
  height: 28px;
  background-color: var(--black_900);
  width: 1px;
  margin-bottom:15px;
}


hr.header-hr {
  border: none;
  height: 3px;
  background-color: var(--deep_orange_300); 
  margin: auto;
  width: 100%;
  max-width: 1440px;
}


.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px;
  background-color: #4CAF50; 
  color: white;
  border-radius: 5px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  font-size: 16px;
  z-index: 1000;
}

.error-message  {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px;
  background-color: #af4c4c; 
  color: white;
  border-radius: 5px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  font-size: 16px;
  z-index: 1000;
}