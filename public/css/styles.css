/*-------------------------------------
  1. CSS Variables
--------------------------------------*/
:root {
  /*------Color variables------*/
  --black_900: #000000;
  --black_900_7f: #0000007f;
  --black_900_8c: #0000008c;
  --blue_gray_50: #f0edf3;
  --blue_gray_900: #222a31;
  --blue_gray_900_00: #222a3100;
  --blue_gray_900_7f: #222a317f;
  --deep_orange_100: #ead4b9;
  --deep_orange_300: #cda565;
  --gray_100: #f3f1f5;
  --gray_300: #ebe2d8;
  --gray_300_01: #e5dbce;
  --gray_800: #46351a;
  --gray_900: #1a2025;
  --gray_900_7f: #1a20257f;
  --white_a700: #ffffff;
  --black_600: #656d76;
  --gray_shadow: #00000029;

  /*------Shadow variables------*/
  --shadow-xs: 0 0 13px 5px #00000029;

  /*------Border radius variables------*/
  --radius-xs: 4px;
  --radius-sm: 8px;
  --radius-md: 10px;
  --radius-lg: 22px;

  /*------Spacing variables------*/
  --space-xs: 6px;
  --space-sm: 8px;
  --space-md: 10px;
  --space-lg: 12px;
  --space-xl: 13px;
  --space-2xl: 14px;
  --space-3xl: 16px;
  --space-4xl: 18px;
  --space-5xl: 20px;
  --space-6xl: 22px;
  --space-7xl: 24px;
  --space-8xl: 28px;
  --space-9xl: 30px;
  --space-10xl: 34px;
  --space-11xl: 40px;
  --space-12xl: 50px;
  --space-13xl: 54px;
  --space-14xl: 78px;
}


/*-------------------------------------
  2. Utility CSS
--------------------------------------*/
.flex-row-center-center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.step {
  width: 100%;
}

.step-buttom {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #b58544;
  color: #fff;
  padding: 15px;
  font-size: 16px;
  border: none;
  border-radius: 0 0 5px 5px; 
  cursor: pointer;
  width: 100%;
  margin-top: 20px;
  box-sizing: border-box;
  font-weight: 700;
}

.step-buttom:hover {
  color: #b58544;
  background-color: #222A31; 
}


.step-indicators {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0px;
  margin-left: 45px;
  margin-right: 45px;
  margin-bottom: 20px;
}

.indicator:hover {
  opacity: 0.8; 
}

.indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.indicator3 {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.indicator-number {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #d3d3d3;
    color: #000;
    font-size: 16px;
    font-weight: bold;
    border: 2px solid transparent;
}

.indicator-number {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #d3d3d3;
  color: #000;
  font-size: 16px;
  font-weight: bold;
  border: 2px solid transparent;
}

.indicator p {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}

.active-step .indicator-number {
  background-color: #fff; 
  border-color: #1dbf73; 
  color: #1dbf73;
}

.completed-step .indicator-number {
  background-color: #1dbf73;
  color: #fff;
  border: 2px solid #1dbf73; 
}

.indicator-line {
  width: 50px;
  margin-bottom: 20px;
  border-top: 1px solid #d3d3d3;
}

.completed-step + hr.indicator-line {
  border-top: 1px solid #1dbf73; 
}


.from-input,
.info-input {
  width: 100%;
  padding: 5px;
  border: none;
  font-size: 16px;
  color: #5d4633;
  background-color: transparent;
  outline: none;
}
@media only screen and (max-width: 550px) {
  .from-input, .info-input {
    width: 100%;
    padding: 1px !important;
    border: none;
    font-size: 13px !important;
    color: #5d4633;
    background-color: transparent;
    outline: none;
}
}

.from-input::placeholder,
.info-input::placeholder {
  font-style: normal !important;
  color: #46351A !important;
}

.to-input::placeholder {
  font-style: normal !important; 
  color: #46351A !important; 
}



.transoprt-type {
  font-size: 16px;
  font-weight: 400;
  font-style: bold;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 20px;
}

.vehicle-type {
  font-size: 16px;
  font-weight: 700;
  font-style: bold;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 25px;
}


.autocomplete-items {
  position: absolute;
  border: 1px solid #d4d4d4;
  background-color: #fff;
  max-height: 200px;
  overflow-y: auto;
  z-index: 99;
}

.autocomplete-items div {
  padding: 10px;
  cursor: pointer;
}

.autocomplete-items div:hover {
  background-color: #e9e9e9;
}

.autocomplete-active {
  background-color: #d4d4d4 !important;
}


.eight {
  font-size: 2em;
  text-align: center;
}

.fix-see-how-it-works {
  max-width : 1200px; 
  margin: 40px auto;
  padding: 20px;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.fix-header-container {
  text-align: center;
  margin-bottom: 40px;
}

.fix-section-title {
  font-family: auto;
  font-size: 32px;
  font-weight: 700;
  color: #333;
}

@media screen and (max-width: 768px) {
  .fix-section-title {
      text-align: left;
  }
}

.fix-steps-container {
    display: flex;
    position: relative;
    overflow: hidden; 
    width: 100%;
}

.fix-step {
  display: none;
  max-width: 100%;
  transition: transform 0.5s ease;
  background-color: #EBE2D8;
  border-radius: 12px;
  padding: 30px;
  gap: 30px;
  flex-shrink: 0;
  align-items: center;
}

.fix-slider-controls {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 10px;
}

.fix-slider-controls {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  gap: 5px;
}

.fix-slider-controls {
  display: flex;
  position: absolute;
  right: 50px;
  bottom: 20px;
  gap: 5px;
  z-index: 10;
}

.fix-slider-controls button {
  background-color: #33333380;
  color: white;
  border: none;
  padding: 5px 10px;
  font-size: 12px;
  width: 50px;
  height: 35px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fix-slider-controls button:hover {
  background-color: #555;
}

.fix-slider-controls button:active {
  background-color: #777;
}

.fix-slider-controls button:disabled {
  background-color: #ddd;
  color: #999;
  cursor: not-allowed;
}

@media (max-width: 600px) {
  .fix-slider-controls {
      right: 10px;
      bottom: 10px;
  }

  .fix-slider-controls button {
      width: 40px;
      height: 30px;
      font-size: 10px;
  }
}





.fix-step.active {
    display: flex;
}



.fix-step-image-left {
  max-width: 416px;
  height: auto;
  margin-right: 5px;
}

.fix-step-image-left-key {
  max-width: 388.92px;
  height: auto;
  margin-right: 5px;
}

.fix-step-image-right {
  max-width: 416px;
  height: auto;
  margin-left: 20px;
}

.fix-step-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-left: 20px;
}

.fix-step-title {
  font-family: auto;
  font-size: 26px;
  color: #333;
  margin-bottom: 10px;
}

.fix-step-description {
  font-family: auto;
  font-size: 18px;
  font-weight: bold;
  color: #555;
  margin-bottom: 10px;
}

.fix-step-details {
  font-family: auto;
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  list-style-type: disc; 
  margin-left: 20px; 
}

.fix-step-details li {
  margin-bottom: 10px; 
  padding-left: 10px; 
}

.fix-reverse-layout {
  flex-direction: row-reverse;
}

@media screen and (max-width: 768px) {
  .fix-step {
      flex-direction: column;
  }

  .fix-step-image-left,
  .fix-step-image-left-key,
  .fix-step-image-right {
      margin: 0 auto 15px auto;
      width: 100%;
  }
}


.success-message, .error-message {
  display: none;
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 20px;
  border-radius: 5px;
  z-index: 9999;
  font-size: 18px;
  color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.success-message {
  background-color: #4CAF50; 
}

.error-message {
  background-color: #f44336; 
}

.fade-out {
  animation: fadeOut 3s forwards;
}

@keyframes fadeOut {
  0% { opacity: 1; }
  90% { opacity: 1; }
  100% { opacity: 0; }
}


.pac-container {
  background-color: #f0f0f0;
  border: 1px solid #b58544;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  font-size: 16px;
  margin-top: 10px;
  width: 310px !important;
  margin-left: -40px !important;
}

.pac-container:after{visibility: hidden!important;position: absolute; top: -9999px; 
  left: -9999px;opacity: 0;width: 0; height: 0;}

.pac-item {
  padding: 10px;
  color: #333;
}

.pac-item:hover {
  background-color: #dcdcdc;
}

.pac-item-query {
  font-weight: bold;
  color: #b58544;
}

.pac-item .pac-item-query + span {
  color: #888;
}

.fix-slider-slider-container {
  max-width: 1152px;
  margin-top: 50px;
  padding: 20px;
  background-color: #EBE2D8;
  border-radius: 8px;
  display: flex;
  gap: 20px;
  @media (max-width: 768px) {
      margin-top: 0px;
      margin-bottom: 0px;
  }
}

.fix-slider-text-section {
  flex: 1;
}

.fix-slider-steps {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.fix-slider-step-button {
  padding: 10px 20px;
  border: 2px solid #b58c4b;
  border-radius: 20px;
  background-color: transparent;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-bottom: 20px;
}

.fix-slider-step-button.active {
  background-color: #CDA565;
  color: white;
  border-color: #b58c4b;
}

.fix-slider-content {
  display: none;
}

.fix-slider-content.active {
  display: block;
}

.fix-slider-title {
  font-size: 20px;
  font-weight: bold;
  color: #222A31;
  margin-bottom: 10px;
  @media only screen and (max-width: 1050px) {
    font-size: 18px;
    margin-bottom: 30px;
  }
}

.fix-slider-description {
  font-size: 16px;
  color: #555;
}

.fix-slider-image-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fix-slider-image-section img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.fix-slider-main-title {
  font-size: 24px;
  font-weight: bold;
  color: #222A31;
  margin-bottom: 35px;
  @media (max-width: 768px) {
      font-size: 22px;
  }
}

@media (max-width: 480px) {
  .fix-slider-image-section {
    display: none;
  }
}


@media (max-width: 767px) { 
  .mobile-break {
      display: block;
  }
}

@media (min-width: 768px) {
  .mobile-break {
      display: none;
  }
}

